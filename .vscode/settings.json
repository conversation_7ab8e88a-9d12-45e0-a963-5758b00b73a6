{
    "files.autoSave": "onFocusChange",
    "editor.formatOnSave": true,
    "editor.tabSize": 4,
    "editor.fontSize": 13,
    "workbench.tree.indent": 16,
    "files.associations": {
        "*.css": "tailwindcss"
    },
    "tailwindCSS.experimental.configFile": "theme/static_src/src/styles.css",
    "[python]": {
        "editor.defaultFormatter": "ms-python.black-formatter"
    },
    "[django-html]": {
        "editor.defaultFormatter": "junstyle.vscode-django-support"
    },
}