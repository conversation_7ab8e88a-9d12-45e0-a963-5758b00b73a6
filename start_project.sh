#!/bin/bash

python3 -m venv venv
source venv/bin/activate
pip install --upgrade pip
pip install -r requirements.txt

# Create a .env file with this content
cat > .env <<EOF
PROJECT_NAME=django-tailwind

DJANGO_SECRET_KEY=django-insecure-0123456789abcdefghijklmnopqrstuvwxyz
DJANGO_DEBUG=True
DJANGO_ALLOWED_HOSTS=*

DB_NAME=django_tailwind_db
DB_USER=django_tailwind_db_user
DB_PASSWORD=showy-coastline-twerp-smudge-sandal-autopilot

EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your_app_password
DEFAULT_FROM_EMAIL=<EMAIL>
CONTACT_RECEIVER_EMAIL=<EMAIL>

ADMIN_NAME=ah
ADMIN_PASSWORD=asdf
EOF

sleep 1

get_env_value() {
  local key="$1"
  local value=$(grep -E "^$key=" .env | sed -E 's/^[^=]+=(.*)$/\1/')
  echo "$value"
}

DB_NAME=$(get_env_value "DB_NAME")
DB_USER=$(get_env_value "DB_USER")
DB_PASSWORD=$(get_env_value "DB_PASSWORD")

# Create database if it doesn't exist
if ! psql -lqt | cut -d \| -f 1 | grep -qw $DB_NAME; then
    psql <<EOF    
    CREATE DATABASE $DB_NAME;
    CREATE USER $DB_USER WITH PASSWORD '$DB_PASSWORD';
    ALTER ROLE $DB_USER SET client_encoding TO 'utf8';
    ALTER ROLE $DB_USER SET default_transaction_isolation TO 'read committed';
    ALTER ROLE $DB_USER SET timezone TO 'Europe/Sofia';
    GRANT ALL PRIVILEGES ON DATABASE $DB_NAME TO $DB_USER;
    \q
EOF
fi

python3 manage.py migrate

cd theme/static_src
npm install

echo "Run:"
echo ""
echo "python manage.py tailwind start"
echo "AND"
echo "python manage.py runserver"

exit 0