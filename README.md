# 🦶 Бос свят - Онлайн магазин за боси обувки

Модерен онлайн магазин за боси обувки, изграден с Django и Tailwind CSS. Проектът предлага пълна функционалност за електронна търговия с фокус върху мобилна адаптивност и SEO оптимизация.

## 🚀 Бърз старт

```bash
./start_project.sh
```

## ✅ Завършени функционалности

-   **Каталог с продукти** - Филтриране по категория, размер, пол и цена
-   **Детайлни продуктови страници** - Галерия със снимки, характеристики, размери
-   **Пазарска количка** - Session-базирана количка с AJAX функционалност
-   **Checkout процес** - Пълен процес за поръчка с валидация
-   **Потребителска автентикация** - Регистрация, вход, профил (django-allauth)
-   **Админ панел** - Пълно управление на продукти, категории, поръчки
-   **Мобилна адаптивност** - Responsive дизайн за всички устройства
-   **SEO оптимизация** - Meta тагове, Schema.org markup, sitemap.xml
-   **Тестове** - Unit тестове за модели и изгледи

## 🛠️ Технологии

-   **Backend**: Django 5.2, Python 3.12
-   **Frontend**: Tailwind CSS, Alpine.js
-   **База данни**: SQLite (development), PostgreSQL (production ready)
-   **Автентикация**: django-allauth
-   **Админ интерфейс**: django-jazzmin

## 🚀 Деплой за продакшън

```bash
./deploy.sh
```

## 📊 SEO функционалности

-   ✅ Meta тагове, Open Graph, Twitter Cards
-   ✅ Schema.org JSON-LD markup
-   ✅ Sitemap.xml и Robots.txt
-   ✅ Canonical URLs и Breadcrumbs
