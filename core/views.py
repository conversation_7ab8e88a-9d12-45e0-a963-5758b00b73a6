from django.shortcuts import render, get_object_or_404, redirect
from django.core.paginator import Paginator
from django.db.models import Q
from django.http import JsonResponse
from django.views.decorators.http import require_POST
from django.contrib import messages
from django.db import transaction
from .models import Product, Category, ShoeSize, Order, OrderItem
from .cart import Cart
from .forms import CheckoutForm


def index(request):
    """Начална страница"""
    featured_products = Product.objects.filter(is_active=True, is_featured=True)[:8]
    categories = Category.objects.all()[:6]

    context = {
        "featured_products": featured_products,
        "categories": categories,
    }
    return render(request, "core/index.html", context)


def product_list(request):
    """Списък с всички продукти с филтри"""
    products = (
        Product.objects.filter(is_active=True)
        .select_related("category")
        .prefetch_related("images")
    )

    # Филтри
    category_slug = request.GET.get("category")
    size_id = request.GET.get("size")
    gender = request.GET.get("gender")
    search = request.GET.get("search")
    sort_by = request.GET.get("sort", "name")

    # Филтриране по категория
    if category_slug:
        products = products.filter(category__slug=category_slug)

    # Филтриране по размер
    if size_id:
        products = products.filter(sizes__id=size_id)

    # Филтриране по пол
    if gender:
        products = products.filter(gender=gender)

    # Търсене
    if search:
        products = products.filter(
            Q(name__icontains=search)
            | Q(description__icontains=search)
            | Q(short_description__icontains=search)
        )

    # Сортиране
    if sort_by == "price_low":
        products = products.order_by("price")
    elif sort_by == "price_high":
        products = products.order_by("-price")
    elif sort_by == "newest":
        products = products.order_by("-created_at")
    else:
        products = products.order_by("name")

    # Пагинация
    paginator = Paginator(products, 12)  # 12 продукта на страница
    page_number = request.GET.get("page")
    page_obj = paginator.get_page(page_number)

    # Данни за филтрите
    categories = Category.objects.all()
    sizes = ShoeSize.objects.all()
    gender_choices = Product._meta.get_field("gender").choices

    context = {
        "page_obj": page_obj,
        "categories": categories,
        "sizes": sizes,
        "gender_choices": gender_choices,
        "current_category": category_slug,
        "current_size": size_id,
        "current_gender": gender,
        "current_search": search,
        "current_sort": sort_by,
    }
    return render(request, "core/product_list.html", context)


def product_detail(request, slug):
    """Детайли на продукт"""
    product = get_object_or_404(
        Product.objects.select_related("category").prefetch_related("images", "sizes"),
        slug=slug,
        is_active=True,
    )

    # Свързани продукти от същата категория
    related_products = Product.objects.filter(
        category=product.category, is_active=True
    ).exclude(id=product.id)[:4]

    context = {
        "product": product,
        "related_products": related_products,
    }
    return render(request, "core/product_detail.html", context)


def category_detail(request, slug):
    """Продукти от определена категория"""
    category = get_object_or_404(Category, slug=slug)
    products = (
        Product.objects.filter(category=category, is_active=True)
        .select_related("category")
        .prefetch_related("images")
    )

    # Филтри
    size_id = request.GET.get("size")
    gender = request.GET.get("gender")
    sort_by = request.GET.get("sort", "name")

    # Филтриране по размер
    if size_id:
        products = products.filter(sizes__id=size_id)

    # Филтриране по пол
    if gender:
        products = products.filter(gender=gender)

    # Сортиране
    if sort_by == "price_low":
        products = products.order_by("price")
    elif sort_by == "price_high":
        products = products.order_by("-price")
    elif sort_by == "newest":
        products = products.order_by("-created_at")
    else:
        products = products.order_by("name")

    # Пагинация
    paginator = Paginator(products, 12)
    page_number = request.GET.get("page")
    page_obj = paginator.get_page(page_number)

    # Данни за филтрите
    sizes = ShoeSize.objects.all()
    gender_choices = Product._meta.get_field("gender").choices

    context = {
        "category": category,
        "page_obj": page_obj,
        "sizes": sizes,
        "gender_choices": gender_choices,
        "current_size": size_id,
        "current_gender": gender,
        "current_sort": sort_by,
    }
    return render(request, "core/category_detail.html", context)


# Изгледи за пазарската количка


@require_POST
def cart_add(request, product_id):
    """Добавя продукт в количката"""
    cart = Cart(request)
    product = get_object_or_404(Product, id=product_id, is_active=True)

    # Вземи количеството от формата (по подразбиране 1)
    quantity = int(request.POST.get("quantity", 1))

    # Провери дали има достатъчно наличност
    if quantity > product.stock:
        messages.error(
            request, f"Няма достатъчно наличност. Налични са само {product.stock} бр."
        )
        return redirect(product.get_absolute_url())

    cart.add(product=product, quantity=quantity)
    messages.success(request, f"{product.name} беше добавен в количката.")

    # Ако заявката е AJAX, върни JSON отговор
    if request.headers.get("X-Requested-With") == "XMLHttpRequest":
        return JsonResponse(
            {
                "success": True,
                "message": f"{product.name} беше добавен в количката.",
                "cart_count": len(cart),
                "cart_total": str(cart.get_total_price()),
            }
        )

    return redirect(product.get_absolute_url())


def cart_detail(request):
    """Показва детайлите на количката"""
    cart = Cart(request)

    context = {
        "cart": cart,
        "cart_items": cart.get_cart_items(),
    }
    return render(request, "core/cart_detail.html", context)


@require_POST
def cart_remove(request, product_id):
    """Премахва продукт от количката"""
    cart = Cart(request)
    product = get_object_or_404(Product, id=product_id)
    cart.remove(product)
    messages.success(request, f"{product.name} беше премахнат от количката.")

    # Ако заявката е AJAX, върни JSON отговор
    if request.headers.get("X-Requested-With") == "XMLHttpRequest":
        return JsonResponse(
            {
                "success": True,
                "message": f"{product.name} беше премахнат от количката.",
                "cart_count": len(cart),
                "cart_total": str(cart.get_total_price()),
            }
        )

    return redirect("core:cart_detail")


@require_POST
def cart_update(request, product_id):
    """Обновява количеството на продукт в количката"""
    cart = Cart(request)
    product = get_object_or_404(Product, id=product_id)

    try:
        quantity = int(request.POST.get("quantity", 0))

        if quantity > 0:
            # Провери дали има достатъчно наличност
            if quantity > product.stock:
                messages.error(
                    request,
                    f"Няма достатъчно наличност. Налични са само {product.stock} бр.",
                )
                return redirect("core:cart_detail")

            cart.update_quantity(product, quantity)
            messages.success(request, "Количката беше обновена.")
        else:
            cart.remove(product)
            messages.success(request, f"{product.name} беше премахнат от количката.")

    except ValueError:
        messages.error(request, "Невалидно количество.")

    # Ако заявката е AJAX, върни JSON отговор
    if request.headers.get("X-Requested-With") == "XMLHttpRequest":
        return JsonResponse(
            {
                "success": True,
                "cart_count": len(cart),
                "cart_total": str(cart.get_total_price()),
            }
        )

    return redirect("core:cart_detail")


def cart_clear(request):
    """Изчиства цялата количка"""
    cart = Cart(request)
    cart.clear()
    messages.success(request, "Количката беше изчистена.")
    return redirect("core:cart_detail")


# Изгледи за checkout и поръчки


def checkout(request):
    """Checkout процес"""
    cart = Cart(request)

    # Провери дали количката е празна
    if cart.is_empty():
        messages.error(
            request, "Количката е празна. Добавете продукти преди да продължите."
        )
        return redirect("core:cart_detail")

    # Провери наличността на всички продукти в количката
    for item in cart:
        if item["quantity"] > item["product"].stock:
            messages.error(
                request,
                f"Няма достатъчно наличност за {item['product'].name}. Налични са само {item['product'].stock} бр.",
            )
            return redirect("core:cart_detail")

    if request.method == "POST":
        form = CheckoutForm(request.POST, user=request.user)
        if form.is_valid():
            try:
                with transaction.atomic():
                    # Създай поръчката
                    order = form.save(commit=False)
                    order.user = request.user if request.user.is_authenticated else None
                    order.total_amount = cart.get_total_price()
                    order.shipping_cost = 0  # Безплатна доставка засега
                    order.save()

                    # Създай артикулите от поръчката
                    for item in cart:
                        OrderItem.objects.create(
                            order=order,
                            product=item["product"],
                            quantity=item["quantity"],
                            price=item["price"],
                        )

                        # Намали наличността на продукта
                        product = item["product"]
                        product.stock -= item["quantity"]
                        product.save()

                    # Изчисти количката
                    cart.clear()

                    # Пренасочи към страница за потвърждение
                    return redirect(
                        "core:order_success", order_number=order.order_number
                    )

            except Exception as e:
                messages.error(
                    request,
                    "Възникна грешка при обработката на поръчката. Моля опитайте отново.",
                )
                return redirect("core:checkout")
    else:
        form = CheckoutForm(user=request.user)

    context = {
        "form": form,
        "cart": cart,
        "cart_items": cart.get_cart_items(),
    }
    return render(request, "core/checkout.html", context)


def order_success(request, order_number):
    """Страница за успешна поръчка"""
    order = get_object_or_404(Order, order_number=order_number)

    # Ако потребителят е логнат, провери дали поръчката е негова
    if request.user.is_authenticated and order.user != request.user:
        messages.error(request, "Нямате достъп до тази поръчка.")
        return redirect("core:index")

    context = {
        "order": order,
    }
    return render(request, "core/order_success.html", context)


def order_detail(request, order_number):
    """Детайли на поръчка"""
    order = get_object_or_404(Order, order_number=order_number)

    # Ако потребителят е логнат, провери дали поръчката е негова
    if request.user.is_authenticated and order.user != request.user:
        messages.error(request, "Нямате достъп до тази поръчка.")
        return redirect("core:index")

    context = {
        "order": order,
    }
    return render(request, "core/order_detail.html", context)
