from decimal import Decimal
from django.conf import settings
from .models import Product


class Cart:
    """Session-базирана пазарска количка"""
    
    def __init__(self, request):
        """Инициализира количката"""
        self.session = request.session
        cart = self.session.get(settings.CART_SESSION_ID)
        if not cart:
            # Запазва празна количка в сесията
            cart = self.session[settings.CART_SESSION_ID] = {}
        self.cart = cart

    def add(self, product, quantity=1, override_quantity=False):
        """
        Добавя продукт в количката или обновява количеството му
        """
        product_id = str(product.id)
        if product_id not in self.cart:
            self.cart[product_id] = {
                'quantity': 0,
                'price': str(product.price)
            }
        if override_quantity:
            self.cart[product_id]['quantity'] = quantity
        else:
            self.cart[product_id]['quantity'] += quantity
        self.save()

    def save(self):
        """Маркира сесията като "модифицирана" за да се запази"""
        self.session.modified = True

    def remove(self, product):
        """Премахва продукт от количката"""
        product_id = str(product.id)
        if product_id in self.cart:
            del self.cart[product_id]
            self.save()

    def __iter__(self):
        """Итерира през елементите в количката и взема продуктите от базата данни"""
        product_ids = self.cart.keys()
        # Взема продуктите от базата данни
        products = Product.objects.filter(id__in=product_ids)
        cart = self.cart.copy()
        for product in products:
            cart[str(product.id)]['product'] = product

        for item in cart.values():
            item['price'] = Decimal(item['price'])
            item['total_price'] = item['price'] * item['quantity']
            yield item

    def __len__(self):
        """Брои всички елементи в количката"""
        return sum(item['quantity'] for item in self.cart.values())

    def get_total_price(self):
        """Изчислява общата цена на всички елементи в количката"""
        return sum(Decimal(item['price']) * item['quantity'] for item in self.cart.values())

    def clear(self):
        """Премахва количката от сесията"""
        del self.session[settings.CART_SESSION_ID]
        self.save()

    def get_item(self, product):
        """Връща елемент от количката за даден продукт"""
        product_id = str(product.id)
        if product_id in self.cart:
            item = self.cart[product_id].copy()
            item['product'] = product
            item['price'] = Decimal(item['price'])
            item['total_price'] = item['price'] * item['quantity']
            return item
        return None

    def update_quantity(self, product, quantity):
        """Обновява количеството на продукт в количката"""
        if quantity > 0:
            self.add(product, quantity=quantity, override_quantity=True)
        else:
            self.remove(product)

    def get_cart_items(self):
        """Връща всички елементи в количката като списък"""
        return list(self)

    def is_empty(self):
        """Проверява дали количката е празна"""
        return len(self.cart) == 0
