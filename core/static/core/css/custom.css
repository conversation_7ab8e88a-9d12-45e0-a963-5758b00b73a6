/* Допълнителни стилове за Бос свят */

/* Анимации */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Hover ефекти */
.hover-lift {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Градиенти */
.gradient-bg {
    background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 50%, #f0fdfa 100%);
}

.gradient-text {
    background: linear-gradient(135deg, #166534, #0f766e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Бутони */
.btn-primary {
    @apply bg-teal-800 text-white px-6 py-3 rounded-lg font-semibold hover:bg-teal-700 transition-all duration-200 transform hover:scale-105;
}

.btn-secondary {
    @apply bg-lime-800 text-white px-6 py-3 rounded-lg font-semibold hover:bg-lime-700 transition-all duration-200 transform hover:scale-105;
}

/* Карти */
.card {
    @apply bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300;
}

.card-hover {
    @apply hover:transform hover:scale-105 transition-all duration-300;
}

/* Форми */
.form-input {
    @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-lime-500 focus:border-transparent transition-all duration-200;
}

.form-input:focus {
    box-shadow: 0 0 0 3px rgba(132, 204, 22, 0.1);
}

/* Навигация */
.nav-link {
    @apply text-gray-700 hover:text-lime-800 transition-colors duration-200 relative;
}

.nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -4px;
    left: 50%;
    background-color: #166534;
    transition: all 0.3s ease-in-out;
    transform: translateX(-50%);
}

.nav-link:hover::after {
    width: 100%;
}

/* Продуктови карти */
.product-card {
    @apply bg-white rounded-xl shadow-md overflow-hidden transition-all duration-300;
}

.product-card:hover {
    @apply shadow-xl transform scale-105;
}

.product-image {
    @apply w-full aspect-square object-cover transition-transform duration-300;
}

.product-card:hover .product-image {
    @apply scale-110;
}

/* Количка */
.cart-badge {
    @apply absolute -top-2 -right-2 bg-teal-800 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* Статуси на поръчки */
.status-pending {
    @apply bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium;
}

.status-confirmed {
    @apply bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium;
}

.status-processing {
    @apply bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-medium;
}

.status-shipped {
    @apply bg-indigo-100 text-indigo-800 px-3 py-1 rounded-full text-sm font-medium;
}

.status-delivered {
    @apply bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium;
}

.status-cancelled {
    @apply bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-medium;
}

/* Responsive подобрения */
@media (max-width: 768px) {
    .mobile-padding {
        @apply px-4;
    }
    
    .mobile-text {
        @apply text-sm;
    }
    
    .mobile-hidden {
        @apply hidden;
    }
    
    .mobile-full {
        @apply w-full;
    }
}

/* Скролбар */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #166534;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #0f766e;
}

/* Loader */
.loader {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #166534;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Tooltip */
.tooltip {
    @apply relative;
}

.tooltip::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #333;
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 14px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s;
    z-index: 1000;
}

.tooltip:hover::before {
    opacity: 1;
    visibility: visible;
    bottom: calc(100% + 8px);
}

/* Печат */
@media print {
    .no-print {
        display: none !important;
    }
    
    .print-only {
        display: block !important;
    }
    
    body {
        font-size: 12pt;
        line-height: 1.4;
    }
    
    .card, .bg-white {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
}
