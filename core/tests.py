from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth.models import User
from decimal import Decimal
from .models import Category, ShoeSize, Product, ProductImage, Order, OrderItem
from .cart import Cart


class ModelTestCase(TestCase):
    """Тестове за моделите"""

    def setUp(self):
        self.category = Category.objects.create(
            name="Мъжки обувки", slug="men-shoes", description="Боси обувки за мъже"
        )

        self.size = ShoeSize.objects.create(
            size="42", eu_size=42, us_size="9", uk_size="8"
        )

        self.product = Product.objects.create(
            name="Тестов продукт",
            slug="test-product",
            description="Тестово описание",
            short_description="Кратко описание",
            price=Decimal("99.99"),
            stock=10,
            category=self.category,
            material="Кожа",
            color="Черен",
            gender="men",
        )
        self.product.sizes.add(self.size)

    def test_category_str(self):
        """Тест за __str__ метода на Category"""
        self.assertEqual(str(self.category), "Мъжки обувки")

    def test_category_get_absolute_url(self):
        """Тест за get_absolute_url метода на Category"""
        expected_url = reverse(
            "core:category_detail", kwargs={"slug": self.category.slug}
        )
        self.assertEqual(self.category.get_absolute_url(), expected_url)

    def test_product_str(self):
        """Тест за __str__ метода на Product"""
        self.assertEqual(str(self.product), "Тестов продукт")

    def test_product_get_absolute_url(self):
        """Тест за get_absolute_url метода на Product"""
        expected_url = reverse(
            "core:product_detail", kwargs={"slug": self.product.slug}
        )
        self.assertEqual(self.product.get_absolute_url(), expected_url)

    def test_product_is_in_stock(self):
        """Тест за is_in_stock property"""
        self.assertTrue(self.product.is_in_stock)

        self.product.stock = 0
        self.product.save()
        self.assertFalse(self.product.is_in_stock)

    def test_product_is_on_sale(self):
        """Тест за is_on_sale property"""
        self.assertFalse(self.product.is_on_sale)

        self.product.old_price = Decimal("120.00")
        self.product.save()
        self.assertTrue(self.product.is_on_sale)

    def test_product_discount_percentage(self):
        """Тест за discount_percentage property"""
        self.product.old_price = Decimal("120.00")
        self.product.save()
        expected_discount = int(((120 - 99.99) / 120) * 100)
        self.assertEqual(self.product.discount_percentage, expected_discount)
