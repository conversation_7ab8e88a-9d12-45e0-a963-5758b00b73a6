from django.db import models
from django.urls import reverse
from django.utils.text import slugify


class Category(models.Model):
    """Модел за категории обувки"""

    name = models.CharField(max_length=100, verbose_name="Име")
    slug = models.SlugField(max_length=100, unique=True, verbose_name="Slug")
    description = models.TextField(blank=True, verbose_name="Описание")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="Създадена на")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="Обновена на")

    class Meta:
        verbose_name = "Категория"
        verbose_name_plural = "Категории"
        ordering = ["name"]

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)

    def get_absolute_url(self):
        return reverse("core:category_detail", kwargs={"slug": self.slug})


class ShoeSize(models.Model):
    """Модел за размери на обувки"""

    size = models.CharField(max_length=10, unique=True, verbose_name="Размер")
    eu_size = models.IntegerField(
        verbose_name="EU размер", help_text="Европейски размер"
    )
    us_size = models.CharField(max_length=10, blank=True, verbose_name="US размер")
    uk_size = models.CharField(max_length=10, blank=True, verbose_name="UK размер")

    class Meta:
        verbose_name = "Размер"
        verbose_name_plural = "Размери"
        ordering = ["eu_size"]

    def __str__(self):
        return f"{self.size} (EU {self.eu_size})"


class Product(models.Model):
    """Модел за продукти (боси обувки)"""

    name = models.CharField(max_length=200, verbose_name="Име")
    slug = models.SlugField(max_length=200, unique=True, verbose_name="Slug")
    description = models.TextField(verbose_name="Описание")
    short_description = models.CharField(
        max_length=300, blank=True, verbose_name="Кратко описание"
    )
    price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="Цена")
    old_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        verbose_name="Стара цена",
    )
    stock = models.PositiveIntegerField(default=0, verbose_name="Наличност")
    is_active = models.BooleanField(default=True, verbose_name="Активен")
    is_featured = models.BooleanField(default=False, verbose_name="Препоръчан")

    # Връзки
    category = models.ForeignKey(
        Category, on_delete=models.CASCADE, verbose_name="Категория"
    )
    sizes = models.ManyToManyField(ShoeSize, verbose_name="Размери")

    # Допълнителни полета
    material = models.CharField(max_length=100, blank=True, verbose_name="Материал")
    color = models.CharField(max_length=50, blank=True, verbose_name="Цвят")
    gender = models.CharField(
        max_length=20,
        choices=[
            ("men", "Мъжки"),
            ("women", "Дамски"),
            ("unisex", "Унисекс"),
            ("kids", "Детски"),
        ],
        default="unisex",
        verbose_name="Пол",
    )

    # Времеви полета
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="Създаден на")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="Обновен на")

    class Meta:
        verbose_name = "Продукт"
        verbose_name_plural = "Продукти"
        ordering = ["-created_at"]

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)

    def get_absolute_url(self):
        return reverse("core:product_detail", kwargs={"slug": self.slug})

    @property
    def is_on_sale(self):
        """Проверява дали продуктът е в промоция"""
        return self.old_price and self.old_price > self.price

    @property
    def discount_percentage(self):
        """Изчислява процента отстъпка"""
        if self.is_on_sale:
            return int(((self.old_price - self.price) / self.old_price) * 100)
        return 0

    @property
    def main_image(self):
        """Връща главната снимка на продукта"""
        return self.images.filter(is_main=True).first() or self.images.first()

    @property
    def is_in_stock(self):
        """Проверява дали продуктът е наличен"""
        return self.stock > 0


class ProductImage(models.Model):
    """Модел за снимки на продукти"""

    product = models.ForeignKey(
        Product, on_delete=models.CASCADE, related_name="images", verbose_name="Продукт"
    )
    image = models.ImageField(upload_to="products/", verbose_name="Снимка")
    alt_text = models.CharField(max_length=200, blank=True, verbose_name="Alt текст")
    is_main = models.BooleanField(default=False, verbose_name="Главна снимка")
    order = models.PositiveIntegerField(default=0, verbose_name="Ред")

    created_at = models.DateTimeField(auto_now_add=True, verbose_name="Създадена на")

    class Meta:
        verbose_name = "Снимка на продукт"
        verbose_name_plural = "Снимки на продукти"
        ordering = ["order", "created_at"]

    def __str__(self):
        return f"Снимка на {self.product.name}"

    def save(self, *args, **kwargs):
        # Ако няма alt текст, използвай името на продукта
        if not self.alt_text:
            self.alt_text = f"Снимка на {self.product.name}"

        # Ако е главна снимка, премахни главната от другите снимки
        if self.is_main:
            ProductImage.objects.filter(product=self.product, is_main=True).update(
                is_main=False
            )

        super().save(*args, **kwargs)


class Order(models.Model):
    """Модел за поръчки"""

    STATUS_CHOICES = [
        ("pending", "Чакаща"),
        ("confirmed", "Потвърдена"),
        ("processing", "Обработва се"),
        ("shipped", "Изпратена"),
        ("delivered", "Доставена"),
        ("cancelled", "Отказана"),
    ]

    # Основна информация
    order_number = models.CharField(
        max_length=20, unique=True, verbose_name="Номер на поръчка"
    )
    user = models.ForeignKey(
        "auth.User",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name="Потребител",
    )
    status = models.CharField(
        max_length=20, choices=STATUS_CHOICES, default="pending", verbose_name="Статус"
    )

    # Контактна информация
    email = models.EmailField(verbose_name="Имейл")
    first_name = models.CharField(max_length=50, verbose_name="Име")
    last_name = models.CharField(max_length=50, verbose_name="Фамилия")
    phone = models.CharField(max_length=20, verbose_name="Телефон")

    # Адрес за доставка
    address = models.CharField(max_length=200, verbose_name="Адрес")
    city = models.CharField(max_length=100, verbose_name="Град")
    postal_code = models.CharField(max_length=20, verbose_name="Пощенски код")
    country = models.CharField(
        max_length=100, default="България", verbose_name="Държава"
    )

    # Финансова информация
    total_amount = models.DecimalField(
        max_digits=10, decimal_places=2, verbose_name="Обща сума"
    )
    shipping_cost = models.DecimalField(
        max_digits=10, decimal_places=2, default=0, verbose_name="Цена за доставка"
    )

    # Допълнителна информация
    notes = models.TextField(blank=True, verbose_name="Бележки")

    # Времеви полета
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="Създадена на")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="Обновена на")

    class Meta:
        verbose_name = "Поръчка"
        verbose_name_plural = "Поръчки"
        ordering = ["-created_at"]

    def __str__(self):
        return f"Поръчка #{self.order_number}"

    def save(self, *args, **kwargs):
        if not self.order_number:
            # Генерира уникален номер на поръчка
            import random
            import string
            from django.utils import timezone

            date_str = timezone.now().strftime("%Y%m%d")
            random_str = "".join(random.choices(string.digits, k=4))
            self.order_number = f"ORD{date_str}{random_str}"

            # Провери дали номерът вече съществува
            while Order.objects.filter(order_number=self.order_number).exists():
                random_str = "".join(random.choices(string.digits, k=4))
                self.order_number = f"ORD{date_str}{random_str}"

        super().save(*args, **kwargs)

    @property
    def full_name(self):
        """Връща пълното име"""
        return f"{self.first_name} {self.last_name}"

    @property
    def full_address(self):
        """Връща пълния адрес"""
        return f"{self.address}, {self.city} {self.postal_code}, {self.country}"

    @property
    def total_with_shipping(self):
        """Връща общата сума с доставка"""
        return self.total_amount + self.shipping_cost


class OrderItem(models.Model):
    """Модел за артикули в поръчка"""

    order = models.ForeignKey(
        Order, on_delete=models.CASCADE, related_name="items", verbose_name="Поръчка"
    )
    product = models.ForeignKey(
        Product, on_delete=models.CASCADE, verbose_name="Продукт"
    )
    quantity = models.PositiveIntegerField(verbose_name="Количество")
    price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="Цена")

    class Meta:
        verbose_name = "Артикул от поръчка"
        verbose_name_plural = "Артикули от поръчки"

    def __str__(self):
        return f"{self.quantity}x {self.product.name} в {self.order}"

    @property
    def total_price(self):
        """Връща общата цена за този артикул"""
        return self.quantity * self.price
