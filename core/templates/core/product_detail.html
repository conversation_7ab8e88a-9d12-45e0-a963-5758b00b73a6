{% extends 'core/base.html' %}
{% load static %}

{% block title %}
    {{ product.name }} - Бос свят
{% endblock %}

{% block description %}
    {{ product.short_description|default:product.description|truncatechars:160 }}
{% endblock %}

{% block content %}
    <div class="max-w-screen-xl mx-auto px-4 py-8">
        <!-- Breadcrumbs -->
        <nav class="mb-8">
            <ol class="flex items-center space-x-2 text-sm text-gray-600">
                <li>
                    <a href="{% url 'core:index' %}" class="hover:text-lime-800">Начало</a>
                </li>
                <li>
                    <span class="mx-2">/</span>
                </li>
                <li>
                    <a href="{% url 'core:product_list' %}" class="hover:text-lime-800">Продукти</a>
                </li>
                <li>
                    <span class="mx-2">/</span>
                </li>
                <li>
                    <a href="{{ product.category.get_absolute_url }}" class="hover:text-lime-800">{{ product.category.name }}</a>
                </li>
                <li>
                    <span class="mx-2">/</span>
                </li>
                <li class="text-gray-900">{{ product.name }}</li>
            </ol>
        </nav>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
            <!-- Галерия със снимки -->
            <div>
                {% if product.images.all %}
                    <!-- Главна снимка -->
                    <div class="mb-4">
                        {% with main_image=product.main_image %}
                            <img id="main-image"
                                src="{% if main_image %}
                                    {{ main_image.image.url }}
                                {% else %}
                                    {{ product.images.first.image.url }}
                                {% endif %}"
                                alt="{% if main_image %}
                                    {{ main_image.alt_text }}
                                {% else %}
                                    {{ product.images.first.alt_text }}
                                {% endif %}"
                                class="w-full aspect-square object-cover rounded-xl shadow-lg" />
                        {% endwith %}
                    </div>

                    <!-- Миниатюри -->
                    {% if product.images.count > 1 %}
                        <div class="grid grid-cols-4 gap-2">
                            {% for image in product.images.all %}
                                <button onclick="changeMainImage('{{ image.image.url }}', '{{ image.alt_text }}')" class="aspect-square rounded-lg overflow-hidden border-2 border-transparent hover:border-lime-800 transition-colors"><img src="{{ image.image.url }}" alt="{{ image.alt_text }}" class="w-full h-full object-cover" /></button>
                            {% endfor %}
                        </div>
                    {% endif %}
                {% else %}
                    <div class="w-full aspect-square bg-gray-200 rounded-xl flex items-center justify-center">
                        <span class="text-gray-500 text-lg">Няма снимки</span>
                    </div>
                {% endif %}
            </div>

            <!-- Информация за продукта -->
            <div>
                <div class="mb-4">
                    <span class="text-sm text-gray-600 bg-gray-100 px-3 py-1 rounded-full">{{ product.category.name }}</span>
                </div>

                <h1 class="text-3xl font-bold text-gray-900 mb-4">{{ product.name }}</h1>

                {% if product.short_description %}
                    <p class="text-xl text-gray-600 mb-6">{{ product.short_description }}</p>
                {% endif %}

                <!-- Цена -->
                <div class="mb-6">
                    <div class="flex items-center space-x-4">
                        <span class="text-3xl font-bold text-teal-800">{{ product.price }} лв.</span>
                        {% if product.old_price %}
                            <span class="text-xl text-gray-500 line-through">{{ product.old_price }} лв.</span>
                            <span class="bg-red-500 text-white px-2 py-1 rounded text-sm font-semibold">-{{ product.discount_percentage }}%</span>
                        {% endif %}
                    </div>
                </div>

                <!-- Характеристики -->
                <div class="mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">Характеристики</h3>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        {% if product.material %}
                            <div>
                                <span class="text-gray-600">Материал:</span>
                                <span class="font-medium">{{ product.material }}</span>
                            </div>
                        {% endif %}
                        {% if product.color %}
                            <div>
                                <span class="text-gray-600">Цвят:</span>
                                <span class="font-medium">{{ product.color }}</span>
                            </div>
                        {% endif %}
                        <div>
                            <span class="text-gray-600">Пол:</span>
                            <span class="font-medium">{{ product.get_gender_display }}</span>
                        </div>
                        <div>
                            <span class="text-gray-600">Наличност:</span>
                            <span class="font-medium {% if product.is_in_stock %}
                                    text-green-600
                                {% else %}
                                    text-red-600
                                {% endif %}">
                                {% if product.is_in_stock %}
                                    В наличност ({{ product.stock }} бр.)
                                {% else %}
                                    Няма в наличност
                                {% endif %}
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Размери -->
                {% if product.sizes.all %}
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">Налични размери</h3>
                        <div class="flex flex-wrap gap-2">
                            {% for size in product.sizes.all %}
                                <span class="px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium hover:border-lime-800 transition-colors">{{ size.size }}</span>
                            {% endfor %}
                        </div>
                    </div>
                {% endif %}

                <!-- Бутони за действие -->
                <div class="mb-8">
                    {% if product.is_in_stock %}
                        <form method="post" action="{% url 'core:cart_add' product.id %}" class="mb-3">
                            {% csrf_token %}
                            <div class="flex items-center space-x-4 mb-4">
                                <label class="text-sm font-medium text-gray-700">Количество:</label>
                                <input type="number" name="quantity" value="1" min="1" max="{{ product.stock }}" class="w-20 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-lime-500" />
                                <span class="text-sm text-gray-500">макс. {{ product.stock }} бр.</span>
                            </div>
                            <button type="submit" class="w-full bg-teal-800 text-white py-3 px-6 rounded-xl font-semibold hover:bg-teal-700 transition-colors">Добави в количката</button>
                        </form>
                    {% else %}
                        <button disabled class="w-full bg-gray-400 text-white py-3 px-6 rounded-xl font-semibold cursor-not-allowed mb-3">Няма в наличност</button>
                    {% endif %}

                    <button class="w-full bg-lime-800 text-white py-3 px-6 rounded-xl font-semibold hover:bg-lime-700 transition-colors">Купи сега</button>
                </div>
            </div>
        </div>

        <!-- Описание -->
        <div class="mb-16">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">Описание</h2>
            <div class="prose max-w-none text-gray-700">{{ product.description|linebreaks }}</div>
        </div>

        <!-- Свързани продукти -->
        {% if related_products %}
            <div class="mb-16">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">Свързани продукти</h2>
                <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6">
                    {% for related_product in related_products %}
                        <div class="bg-white rounded-xl shadow-md hover:shadow-lg transition-shadow overflow-hidden">
                            <div class="aspect-square bg-gray-100 relative">
                                {% if related_product.main_image %}
                                    <img src="{{ related_product.main_image.image.url }}" alt="{{ related_product.main_image.alt_text }}" class="w-full h-full object-cover" />
                                {% else %}
                                    <div class="w-full h-full flex items-center justify-center text-gray-400">Няма снимка</div>
                                {% endif %}

                                {% if related_product.is_on_sale %}
                                    <div class="absolute top-2 right-2 bg-red-500 text-white px-2 py-1 rounded-lg text-sm font-semibold">-{{ related_product.discount_percentage }}%</div>
                                {% endif %}
                            </div>

                            <div class="p-4">
                                <h3 class="font-semibold text-gray-900 mb-2">{{ related_product.name }}</h3>
                                <p class="text-gray-600 text-sm mb-3">{{ related_product.short_description|truncatechars:50 }}</p>

                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        <span class="text-lg font-bold text-teal-800">{{ related_product.price }} лв.</span>
                                        {% if related_product.old_price %}
                                            <span class="text-sm text-gray-500 line-through">{{ related_product.old_price }} лв.</span>
                                        {% endif %}
                                    </div>

                                    <a href="{{ related_product.get_absolute_url }}" class="bg-lime-800 text-white px-4 py-2 rounded-lg text-sm font-semibold hover:bg-lime-700 transition-colors">Виж</a>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
        {% endif %}
    </div>

    <script>
        function changeMainImage(imageUrl, altText) {
            const mainImage = document.getElementById('main-image')
            mainImage.src = imageUrl
            mainImage.alt = altText
        }
    </script>
{% endblock %}
