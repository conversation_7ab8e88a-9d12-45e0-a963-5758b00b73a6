{% extends 'core/base.html' %}
{% load static %}

{% block title %}
    Пазарска количка - Бос свят
{% endblock %}

{% block content %}
    <div class="max-w-screen-xl mx-auto px-4 py-8">
        <!-- Breadcrumbs -->
        <nav class="mb-8">
            <ol class="flex items-center space-x-2 text-sm text-gray-600">
                <li>
                    <a href="{% url 'core:index' %}" class="hover:text-lime-800">Начало</a>
                </li>
                <li>
                    <span class="mx-2">/</span>
                </li>
                <li class="text-gray-900">Пазарска количка</li>
            </ol>
        </nav>

        <h1 class="text-3xl font-bold text-gray-900 mb-8">Пазарска количка</h1>

        {% if messages %}
            {% for message in messages %}
                <div class="mb-4 p-4 rounded-lg {% if message.tags == 'error' %}
                        bg-red-100 text-red-700
                    {% elif message.tags == 'success' %}
                        bg-green-100 text-green-700
                    {% else %}
                        bg-blue-100 text-blue-700
                    {% endif %}">{{ message }}</div>
            {% endfor %}
        {% endif %}

        {% if cart_items %}
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Списък с продукти -->
                <div class="lg:col-span-2">
                    <div class="bg-white rounded-lg shadow-md overflow-hidden">
                        <!-- Desktop заглавие -->
                        <div class="hidden md:grid grid-cols-12 gap-4 p-4 bg-gray-50 border-b font-semibold text-gray-700">
                            <div class="col-span-6">Продукт</div>
                            <div class="col-span-2 text-center">Цена</div>
                            <div class="col-span-2 text-center">Количество</div>
                            <div class="col-span-2 text-center">Общо</div>
                        </div>

                        <!-- Продукти -->
                        {% for item in cart_items %}
                            <div class="p-4 border-b last:border-b-0">
                                <!-- Mobile изглед -->
                                <div class="md:hidden">
                                    <div class="flex items-start space-x-4">
                                        <!-- Снимка -->
                                        <div class="w-20 h-20 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
                                            {% if item.product.main_image %}
                                                <img src="{{ item.product.main_image.image.url }}" alt="{{ item.product.main_image.alt_text }}" class="w-full h-full object-cover" />
                                            {% else %}
                                                <div class="w-full h-full flex items-center justify-center text-gray-400 text-xs">Няма снимка</div>
                                            {% endif %}
                                        </div>

                                        <!-- Информация -->
                                        <div class="flex-1">
                                            <h3 class="font-semibold text-gray-900 mb-1">{{ item.product.name }}</h3>
                                            <p class="text-sm text-gray-600 mb-2">{{ item.product.category.name }}</p>

                                            <div class="flex items-center justify-between">
                                                <div class="flex items-center space-x-2">
                                                    <span class="text-lg font-bold text-teal-800">{{ item.price }} лв.</span>
                                                    <span class="text-sm text-gray-500">x {{ item.quantity }}</span>
                                                </div>
                                                <span class="text-lg font-bold text-gray-900">{{ item.total_price }} лв.</span>
                                            </div>

                                            <!-- Бутони за мобилно -->
                                            <div class="flex items-center justify-between mt-3">
                                                <form method="post" action="{% url 'core:cart_update' item.product.id %}" class="flex items-center space-x-2">
                                                    {% csrf_token %}
                                                    <input type="number" name="quantity" value="{{ item.quantity }}" min="1" max="{{ item.product.stock }}" class="w-16 px-2 py-1 border border-gray-300 rounded text-center text-sm" />
                                                    <button type="submit" class="bg-lime-800 text-white px-3 py-1 rounded text-sm hover:bg-lime-700 transition-colors">Обнови</button>
                                                </form>

                                                <form method="post" action="{% url 'core:cart_remove' item.product.id %}">
                                                    {% csrf_token %}
                                                    <button type="submit" class="text-red-600 hover:text-red-800 text-sm">Премахни</button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Desktop изглед -->
                                <div class="hidden md:grid grid-cols-12 gap-4 items-center">
                                    <!-- Продукт -->
                                    <div class="col-span-6 flex items-center space-x-4">
                                        <div class="w-16 h-16 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
                                            {% if item.product.main_image %}
                                                <img src="{{ item.product.main_image.image.url }}" alt="{{ item.product.main_image.alt_text }}" class="w-full h-full object-cover" />
                                            {% else %}
                                                <div class="w-full h-full flex items-center justify-center text-gray-400 text-xs">Няма снимка</div>
                                            {% endif %}
                                        </div>
                                        <div>
                                            <h3 class="font-semibold text-gray-900">{{ item.product.name }}</h3>
                                            <p class="text-sm text-gray-600">{{ item.product.category.name }}</p>
                                        </div>
                                    </div>

                                    <!-- Цена -->
                                    <div class="col-span-2 text-center">
                                        <span class="font-semibold text-teal-800">{{ item.price }} лв.</span>
                                    </div>

                                    <!-- Количество -->
                                    <div class="col-span-2 text-center">
                                        <form method="post" action="{% url 'core:cart_update' item.product.id %}" class="flex items-center justify-center space-x-2">
                                            {% csrf_token %}
                                            <input type="number" name="quantity" value="{{ item.quantity }}" min="1" max="{{ item.product.stock }}" class="w-16 px-2 py-1 border border-gray-300 rounded text-center" />
                                            <button type="submit" class="bg-lime-800 text-white px-2 py-1 rounded text-sm hover:bg-lime-700 transition-colors">✓</button>
                                            <form method="post" action="{% url 'core:cart_remove' item.product.id %}" class="inline">
                                                {% csrf_token %}
                                                <button type="submit" class="text-red-600 hover:text-red-800 text-sm">✕</button>
                                            </form>
                                        </form>
                                    </div>

                                    <!-- Общо -->
                                    <div class="col-span-2 text-center">
                                        <span class="font-bold text-gray-900">{{ item.total_price }} лв.</span>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>

                <!-- Резюме на поръчката -->
                <div class="lg:col-span-1">
                    <div class="bg-white rounded-lg shadow-md p-6 sticky top-24">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Резюме на поръчката</h3>

                        <div class="space-y-3 mb-6">
                            <div class="flex justify-between text-gray-600">
                                <span>Продукти ({{ cart|length }} бр.)</span>
                                <span>{{ cart.get_total_price }} лв.</span>
                            </div>
                            <div class="flex justify-between text-gray-600">
                                <span>Доставка</span>
                                <span>Безплатна</span>
                            </div>
                            <div class="border-t pt-3">
                                <div class="flex justify-between text-lg font-bold text-gray-900">
                                    <span>Общо</span>
                                    <span>{{ cart.get_total_price }} лв.</span>
                                </div>
                            </div>
                        </div>

                        <div class="space-y-3">
                            <a href="{% url 'core:checkout' %}" class="block w-full bg-teal-800 text-white py-3 px-4 rounded-lg font-semibold hover:bg-teal-700 transition-colors text-center">Продължи към поръчка</a>

                            <a href="{% url 'core:product_list' %}" class="block w-full bg-lime-800 text-white py-3 px-4 rounded-lg font-semibold hover:bg-lime-700 transition-colors text-center">Продължи пазаруването</a>

                            <a href="{% url 'core:cart_clear' %}" class="block w-full text-center text-red-600 hover:text-red-800 py-2 text-sm">Изчисти количката</a>
                        </div>
                    </div>
                </div>
            </div>
        {% else %}
            <!-- Празна количка -->
            <div class="text-center py-16">
                <div class="mb-8">
                    <svg class="w-24 h-24 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01"></path>
                    </svg>
                    <h2 class="text-2xl font-bold text-gray-900 mb-2">Количката е празна</h2>
                    <p class="text-gray-600 mb-8">Добавете продукти в количката, за да продължите с поръчката.</p>
                </div>

                <a href="{% url 'core:product_list' %}" class="inline-block bg-teal-800 text-white px-8 py-3 rounded-lg font-semibold hover:bg-teal-700 transition-colors">Разгледайте продуктите</a>
            </div>
        {% endif %}
    </div>
{% endblock %}
