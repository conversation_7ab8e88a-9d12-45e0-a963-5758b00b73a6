{% extends 'core/base.html' %}
{% load static %}

{% block title %}Поръчка - Бос свят{% endblock %}

{% block content %}
    <div class="max-w-screen-xl mx-auto px-4 py-8">
        <!-- Breadcrumbs -->
        <nav class="mb-8">
            <ol class="flex items-center space-x-2 text-sm text-gray-600">
                <li><a href="{% url 'core:index' %}" class="hover:text-lime-800">Начало</a></li>
                <li><span class="mx-2">/</span></li>
                <li><a href="{% url 'core:cart_detail' %}" class="hover:text-lime-800">Количка</a></li>
                <li><span class="mx-2">/</span></li>
                <li class="text-gray-900">Поръчка</li>
            </ol>
        </nav>

        <h1 class="text-3xl font-bold text-gray-900 mb-8">Финализиране на поръчката</h1>

        {% if messages %}
            {% for message in messages %}
                <div class="mb-4 p-4 rounded-lg {% if message.tags == 'error' %}bg-red-100 text-red-700{% elif message.tags == 'success' %}bg-green-100 text-green-700{% else %}bg-blue-100 text-blue-700{% endif %}">
                    {{ message }}
                </div>
            {% endfor %}
        {% endif %}

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Форма за поръчка -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-6">Данни за доставка</h2>
                
                <form method="post" action="{% url 'core:checkout' %}">
                    {% csrf_token %}
                    
                    <!-- Лични данни -->
                    <div class="mb-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Лични данни</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="{{ form.first_name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                                    {{ form.first_name.label }}
                                </label>
                                {{ form.first_name }}
                                {% if form.first_name.errors %}
                                    <div class="mt-1 text-sm text-red-600">
                                        {{ form.first_name.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div>
                                <label for="{{ form.last_name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                                    {{ form.last_name.label }}
                                </label>
                                {{ form.last_name }}
                                {% if form.last_name.errors %}
                                    <div class="mt-1 text-sm text-red-600">
                                        {{ form.last_name.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                            <div>
                                <label for="{{ form.email.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                                    {{ form.email.label }}
                                </label>
                                {{ form.email }}
                                {% if form.email.errors %}
                                    <div class="mt-1 text-sm text-red-600">
                                        {{ form.email.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div>
                                <label for="{{ form.phone.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                                    {{ form.phone.label }}
                                </label>
                                {{ form.phone }}
                                {% if form.phone.errors %}
                                    <div class="mt-1 text-sm text-red-600">
                                        {{ form.phone.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Адрес за доставка -->
                    <div class="mb-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Адрес за доставка</h3>
                        
                        <div class="mb-4">
                            <label for="{{ form.address.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                                {{ form.address.label }}
                            </label>
                            {{ form.address }}
                            {% if form.address.errors %}
                                <div class="mt-1 text-sm text-red-600">
                                    {{ form.address.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label for="{{ form.city.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                                    {{ form.city.label }}
                                </label>
                                {{ form.city }}
                                {% if form.city.errors %}
                                    <div class="mt-1 text-sm text-red-600">
                                        {{ form.city.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div>
                                <label for="{{ form.postal_code.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                                    {{ form.postal_code.label }}
                                </label>
                                {{ form.postal_code }}
                                {% if form.postal_code.errors %}
                                    <div class="mt-1 text-sm text-red-600">
                                        {{ form.postal_code.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div>
                                <label for="{{ form.country.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                                    {{ form.country.label }}
                                </label>
                                {{ form.country }}
                                {% if form.country.errors %}
                                    <div class="mt-1 text-sm text-red-600">
                                        {{ form.country.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Бележки -->
                    <div class="mb-6">
                        <label for="{{ form.notes.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            {{ form.notes.label }}
                        </label>
                        {{ form.notes }}
                        {% if form.notes.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ form.notes.errors.0 }}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Бутони -->
                    <div class="flex flex-col sm:flex-row gap-4">
                        <button type="submit" 
                                class="flex-1 bg-teal-800 text-white py-3 px-6 rounded-lg font-semibold hover:bg-teal-700 transition-colors">
                            Потвърди поръчката
                        </button>
                        
                        <a href="{% url 'core:cart_detail' %}" 
                           class="flex-1 bg-gray-300 text-gray-700 py-3 px-6 rounded-lg font-semibold hover:bg-gray-400 transition-colors text-center">
                            Назад към количката
                        </a>
                    </div>
                </form>
            </div>

            <!-- Резюме на поръчката -->
            <div class="bg-white rounded-lg shadow-md p-6 h-fit sticky top-24">
                <h2 class="text-xl font-semibold text-gray-900 mb-6">Резюме на поръчката</h2>
                
                <!-- Продукти -->
                <div class="space-y-4 mb-6">
                    {% for item in cart_items %}
                    <div class="flex items-center space-x-4 pb-4 border-b border-gray-200 last:border-b-0">
                        <div class="w-16 h-16 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
                            {% if item.product.main_image %}
                                <img src="{{ item.product.main_image.image.url }}" 
                                     alt="{{ item.product.main_image.alt_text }}"
                                     class="w-full h-full object-cover">
                            {% else %}
                                <div class="w-full h-full flex items-center justify-center text-gray-400 text-xs">
                                    Няма снимка
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="flex-1">
                            <h3 class="font-medium text-gray-900">{{ item.product.name }}</h3>
                            <p class="text-sm text-gray-600">{{ item.product.category.name }}</p>
                            <div class="flex items-center justify-between mt-1">
                                <span class="text-sm text-gray-500">{{ item.quantity }} x {{ item.price }} лв.</span>
                                <span class="font-medium text-gray-900">{{ item.total_price }} лв.</span>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                <!-- Общо -->
                <div class="space-y-3 pt-4 border-t border-gray-200">
                    <div class="flex justify-between text-gray-600">
                        <span>Продукти ({{ cart|length }} бр.)</span>
                        <span>{{ cart.get_total_price }} лв.</span>
                    </div>
                    <div class="flex justify-between text-gray-600">
                        <span>Доставка</span>
                        <span>Безплатна</span>
                    </div>
                    <div class="flex justify-between text-lg font-bold text-gray-900 pt-3 border-t border-gray-200">
                        <span>Общо</span>
                        <span>{{ cart.get_total_price }} лв.</span>
                    </div>
                </div>
                
                <!-- Информация за доставка -->
                <div class="mt-6 p-4 bg-gray-50 rounded-lg">
                    <h4 class="font-medium text-gray-900 mb-2">Информация за доставка</h4>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• Безплатна доставка в цяла България</li>
                        <li>• Доставка до 3-5 работни дни</li>
                        <li>• Плащане при доставка</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
