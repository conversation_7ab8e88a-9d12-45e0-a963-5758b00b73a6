{% extends 'core/base.html' %}
{% load static %}

{% block title %}Продукти - Бос свят{% endblock %}

{% block content %}
    <div class="max-w-screen-xl mx-auto px-4 py-8">
        <!-- Breadcrumbs -->
        <nav class="mb-8">
            <ol class="flex items-center space-x-2 text-sm text-gray-600">
                <li><a href="{% url 'core:index' %}" class="hover:text-lime-800">Начало</a></li>
                <li><span class="mx-2">/</span></li>
                <li class="text-gray-900">Продукти</li>
            </ol>
        </nav>

        <!-- Заглавие и търсене -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-4">Всички продукти</h1>
            
            <!-- Търсене -->
            <form method="get" class="mb-6">
                <div class="flex flex-col sm:flex-row gap-4">
                    <input type="text" 
                           name="search" 
                           value="{{ current_search }}"
                           placeholder="Търсете продукти..."
                           class="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-lime-500 focus:border-transparent">
                    <button type="submit" 
                            class="bg-lime-800 text-white px-6 py-2 rounded-lg hover:bg-lime-700 transition-colors">
                        Търси
                    </button>
                </div>
            </form>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <!-- Sidebar с филтри -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-lg shadow-md p-6 lg:sticky lg:top-24" x-data="{ filtersOpen: false }">
                    <!-- Mobile filter toggle -->
                    <div class="lg:hidden mb-4">
                        <button @click="filtersOpen = !filtersOpen"
                                class="flex items-center justify-between w-full text-lg font-semibold text-gray-900">
                            <span>Филтри</span>
                            <svg class="w-5 h-5 transform transition-transform" :class="{ 'rotate-180': filtersOpen }"
                                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                    </div>

                    <!-- Desktop title -->
                    <h3 class="hidden lg:block text-lg font-semibold text-gray-900 mb-4">Филтри</h3>

                    <!-- Filter form - collapsible on mobile -->
                    <div x-show="filtersOpen || window.innerWidth >= 1024" x-transition class="lg:block">
                        <form method="get" id="filter-form">
                        {% if current_search %}
                            <input type="hidden" name="search" value="{{ current_search }}">
                        {% endif %}
                        
                        <!-- Категории -->
                        <div class="mb-6">
                            <h4 class="font-medium text-gray-900 mb-3">Категория</h4>
                            <div class="space-y-2">
                                <label class="flex items-center">
                                    <input type="radio" name="category" value="" 
                                           {% if not current_category %}checked{% endif %}
                                           class="text-lime-600 focus:ring-lime-500">
                                    <span class="ml-2 text-gray-700">Всички</span>
                                </label>
                                {% for category in categories %}
                                <label class="flex items-center">
                                    <input type="radio" name="category" value="{{ category.slug }}"
                                           {% if current_category == category.slug %}checked{% endif %}
                                           class="text-lime-600 focus:ring-lime-500">
                                    <span class="ml-2 text-gray-700">{{ category.name }}</span>
                                </label>
                                {% endfor %}
                            </div>
                        </div>

                        <!-- Размери -->
                        <div class="mb-6">
                            <h4 class="font-medium text-gray-900 mb-3">Размер</h4>
                            <select name="size" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-lime-500">
                                <option value="">Всички размери</option>
                                {% for size in sizes %}
                                <option value="{{ size.id }}" {% if current_size == size.id|stringformat:"s" %}selected{% endif %}>
                                    {{ size }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>

                        <!-- Пол -->
                        <div class="mb-6">
                            <h4 class="font-medium text-gray-900 mb-3">Пол</h4>
                            <div class="space-y-2">
                                <label class="flex items-center">
                                    <input type="radio" name="gender" value=""
                                           {% if not current_gender %}checked{% endif %}
                                           class="text-lime-600 focus:ring-lime-500">
                                    <span class="ml-2 text-gray-700">Всички</span>
                                </label>
                                {% for value, label in gender_choices %}
                                <label class="flex items-center">
                                    <input type="radio" name="gender" value="{{ value }}"
                                           {% if current_gender == value %}checked{% endif %}
                                           class="text-lime-600 focus:ring-lime-500">
                                    <span class="ml-2 text-gray-700">{{ label }}</span>
                                </label>
                                {% endfor %}
                            </div>
                        </div>

                        <button type="submit" 
                                class="w-full bg-teal-800 text-white py-2 rounded-lg hover:bg-teal-700 transition-colors">
                            Приложи филтри
                        </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Продукти -->
            <div class="lg:col-span-3">
                <!-- Сортиране -->
                <div class="flex justify-between items-center mb-6">
                    <p class="text-gray-600">
                        Показани {{ page_obj.start_index }}-{{ page_obj.end_index }} от {{ page_obj.paginator.count }} продукта
                    </p>
                    
                    <form method="get" class="flex items-center space-x-2">
                        {% if current_search %}<input type="hidden" name="search" value="{{ current_search }}">{% endif %}
                        {% if current_category %}<input type="hidden" name="category" value="{{ current_category }}">{% endif %}
                        {% if current_size %}<input type="hidden" name="size" value="{{ current_size }}">{% endif %}
                        {% if current_gender %}<input type="hidden" name="gender" value="{{ current_gender }}">{% endif %}
                        
                        <label class="text-sm text-gray-700">Сортирай по:</label>
                        <select name="sort" onchange="this.form.submit()" 
                                class="px-3 py-1 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-lime-500">
                            <option value="name" {% if current_sort == 'name' %}selected{% endif %}>Име</option>
                            <option value="price_low" {% if current_sort == 'price_low' %}selected{% endif %}>Цена (ниска към висока)</option>
                            <option value="price_high" {% if current_sort == 'price_high' %}selected{% endif %}>Цена (висока към ниска)</option>
                            <option value="newest" {% if current_sort == 'newest' %}selected{% endif %}>Най-нови</option>
                        </select>
                    </form>
                </div>

                <!-- Grid с продукти -->
                {% if page_obj %}
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                    {% for product in page_obj %}
                    <div class="bg-white rounded-xl shadow-md hover:shadow-lg transition-shadow overflow-hidden">
                        <div class="aspect-square bg-gray-100 relative">
                            {% if product.main_image %}
                                <img src="{{ product.main_image.image.url }}" 
                                     alt="{{ product.main_image.alt_text }}"
                                     class="w-full h-full object-cover">
                            {% else %}
                                <div class="w-full h-full flex items-center justify-center text-gray-400">
                                    Няма снимка
                                </div>
                            {% endif %}
                            
                            {% if product.is_on_sale %}
                            <div class="absolute top-2 right-2 bg-red-500 text-white px-2 py-1 rounded-lg text-sm font-semibold">
                                -{{ product.discount_percentage }}%
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="p-4">
                            <h3 class="font-semibold text-gray-900 mb-2">{{ product.name }}</h3>
                            <p class="text-gray-600 text-sm mb-3">{{ product.short_description|truncatechars:60 }}</p>
                            
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <span class="text-lg font-bold text-teal-800">{{ product.price }} лв.</span>
                                    {% if product.old_price %}
                                    <span class="text-sm text-gray-500 line-through">{{ product.old_price }} лв.</span>
                                    {% endif %}
                                </div>
                                
                                <a href="{{ product.get_absolute_url }}" 
                                   class="bg-lime-800 text-white px-4 py-2 rounded-lg text-sm font-semibold hover:bg-lime-700 transition-colors">
                                    Виж
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <!-- Пагинация -->
                {% if page_obj.has_other_pages %}
                <div class="flex justify-center">
                    <nav class="flex items-center space-x-2">
                        {% if page_obj.has_previous %}
                            <a href="?page={{ page_obj.previous_page_number }}{% if current_search %}&search={{ current_search }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}{% if current_size %}&size={{ current_size }}{% endif %}{% if current_gender %}&gender={{ current_gender }}{% endif %}{% if current_sort %}&sort={{ current_sort }}{% endif %}" 
                               class="px-3 py-2 text-gray-500 hover:text-gray-700">
                                Предишна
                            </a>
                        {% endif %}

                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <span class="px-3 py-2 bg-lime-800 text-white rounded">{{ num }}</span>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <a href="?page={{ num }}{% if current_search %}&search={{ current_search }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}{% if current_size %}&size={{ current_size }}{% endif %}{% if current_gender %}&gender={{ current_gender }}{% endif %}{% if current_sort %}&sort={{ current_sort }}{% endif %}" 
                                   class="px-3 py-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded">
                                    {{ num }}
                                </a>
                            {% endif %}
                        {% endfor %}

                        {% if page_obj.has_next %}
                            <a href="?page={{ page_obj.next_page_number }}{% if current_search %}&search={{ current_search }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}{% if current_size %}&size={{ current_size }}{% endif %}{% if current_gender %}&gender={{ current_gender }}{% endif %}{% if current_sort %}&sort={{ current_sort }}{% endif %}" 
                               class="px-3 py-2 text-gray-500 hover:text-gray-700">
                                Следваща
                            </a>
                        {% endif %}
                    </nav>
                </div>
                {% endif %}
                {% else %}
                <div class="text-center py-12">
                    <p class="text-gray-500 text-lg">Няма намерени продукти.</p>
                    <a href="{% url 'core:product_list' %}" 
                       class="inline-block mt-4 bg-lime-800 text-white px-6 py-2 rounded-lg hover:bg-lime-700 transition-colors">
                        Виж всички продукти
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
{% endblock %}
