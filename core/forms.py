from django import forms
from .models import Order


class CheckoutForm(forms.ModelForm):
    """Форма за checkout процеса"""
    
    class Meta:
        model = Order
        fields = [
            'first_name', 'last_name', 'email', 'phone',
            'address', 'city', 'postal_code', 'country', 'notes'
        ]
        widgets = {
            'first_name': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-lime-500 focus:border-transparent',
                'placeholder': 'Име'
            }),
            'last_name': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-lime-500 focus:border-transparent',
                'placeholder': 'Фамилия'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-lime-500 focus:border-transparent',
                'placeholder': 'имейл@example.com'
            }),
            'phone': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-lime-500 focus:border-transparent',
                'placeholder': '+359 888 123 456'
            }),
            'address': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-lime-500 focus:border-transparent',
                'placeholder': 'ул. Примерна 123'
            }),
            'city': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-lime-500 focus:border-transparent',
                'placeholder': 'София'
            }),
            'postal_code': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-lime-500 focus:border-transparent',
                'placeholder': '1000'
            }),
            'country': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-lime-500 focus:border-transparent',
                'placeholder': 'България'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-lime-500 focus:border-transparent',
                'placeholder': 'Допълнителни бележки за поръчката (незадължително)',
                'rows': 3
            }),
        }
        labels = {
            'first_name': 'Име *',
            'last_name': 'Фамилия *',
            'email': 'Имейл адрес *',
            'phone': 'Телефон *',
            'address': 'Адрес *',
            'city': 'Град *',
            'postal_code': 'Пощенски код *',
            'country': 'Държава *',
            'notes': 'Бележки',
        }

    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        # Ако потребителят е логнат, попълни данните му
        if user and user.is_authenticated:
            self.fields['email'].initial = user.email
            if hasattr(user, 'first_name') and user.first_name:
                self.fields['first_name'].initial = user.first_name
            if hasattr(user, 'last_name') and user.last_name:
                self.fields['last_name'].initial = user.last_name

    def clean_phone(self):
        phone = self.cleaned_data.get('phone')
        if phone:
            # Премахни всички символи освен цифри и +
            import re
            phone = re.sub(r'[^\d+]', '', phone)
            if not phone.startswith('+'):
                # Ако не започва с +, добави +359 за България
                if phone.startswith('0'):
                    phone = '+359' + phone[1:]
                else:
                    phone = '+359' + phone
        return phone

    def clean_postal_code(self):
        postal_code = self.cleaned_data.get('postal_code')
        if postal_code:
            # Премахни всички символи освен цифри
            import re
            postal_code = re.sub(r'[^\d]', '', postal_code)
        return postal_code
