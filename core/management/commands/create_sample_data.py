from django.core.management.base import BaseCommand
from django.core.files.base import ContentFile
from django.conf import settings
import os
from core.models import Category, ShoeSize, Product, ProductImage
from decimal import Decimal


class Command(BaseCommand):
    help = 'Създава тестови данни за демонстрация на магазина'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Започвам създаването на тестови данни...'))

        # Създаване на размери
        self.create_sizes()
        
        # Създаване на категории
        self.create_categories()
        
        # Създаване на продукти
        self.create_products()
        
        self.stdout.write(self.style.SUCCESS('Тестовите данни са създадени успешно!'))

    def create_sizes(self):
        """Създава размери за обувки"""
        sizes_data = [
            {'size': '35', 'eu_size': 35, 'us_size': '5', 'uk_size': '2.5'},
            {'size': '36', 'eu_size': 36, 'us_size': '5.5', 'uk_size': '3'},
            {'size': '37', 'eu_size': 37, 'us_size': '6', 'uk_size': '4'},
            {'size': '38', 'eu_size': 38, 'us_size': '7', 'uk_size': '5'},
            {'size': '39', 'eu_size': 39, 'us_size': '8', 'uk_size': '6'},
            {'size': '40', 'eu_size': 40, 'us_size': '9', 'uk_size': '7'},
            {'size': '41', 'eu_size': 41, 'us_size': '10', 'uk_size': '8'},
            {'size': '42', 'eu_size': 42, 'us_size': '11', 'uk_size': '9'},
            {'size': '43', 'eu_size': 43, 'us_size': '12', 'uk_size': '10'},
            {'size': '44', 'eu_size': 44, 'us_size': '13', 'uk_size': '11'},
            {'size': '45', 'eu_size': 45, 'us_size': '14', 'uk_size': '12'},
        ]
        
        for size_data in sizes_data:
            size, created = ShoeSize.objects.get_or_create(
                eu_size=size_data['eu_size'],
                defaults=size_data
            )
            if created:
                self.stdout.write(f'Създаден размер: {size}')

    def create_categories(self):
        """Създава категории"""
        categories_data = [
            {
                'name': 'Мъжки боси обувки',
                'slug': 'muzhki-bosi-obuvki',
                'description': 'Боси обувки за мъже - комфорт и естественост при всяка стъпка'
            },
            {
                'name': 'Дамски боси обувки', 
                'slug': 'damski-bosi-obuvki',
                'description': 'Елегантни и удобни боси обувки за жени'
            },
            {
                'name': 'Детски боси обувки',
                'slug': 'detski-bosi-obuvki', 
                'description': 'Здравословно развитие на детските крачета с боси обувки'
            },
            {
                'name': 'Спортни боси обувки',
                'slug': 'sportni-bosi-obuvki',
                'description': 'За активен начин на живот и спорт'
            },
        ]
        
        for cat_data in categories_data:
            category, created = Category.objects.get_or_create(
                slug=cat_data['slug'],
                defaults=cat_data
            )
            if created:
                self.stdout.write(f'Създадена категория: {category.name}')

    def create_products(self):
        """Създава продукти"""
        # Вземи категориите и размерите
        men_category = Category.objects.get(slug='muzhki-bosi-obuvki')
        women_category = Category.objects.get(slug='damski-bosi-obuvki')
        kids_category = Category.objects.get(slug='detski-bosi-obuvki')
        sport_category = Category.objects.get(slug='sportni-bosi-obuvki')
        
        products_data = [
            {
                'name': 'Класически мъжки боси обувки',
                'slug': 'klasicheski-muzhki-bosi-obuvki',
                'category': men_category,
                'price': Decimal('89.99'),
                'old_price': Decimal('109.99'),
                'stock': 25,
                'gender': 'men',
                'material': 'Естествена кожа',
                'color': 'Кафяв',
                'short_description': 'Елегантни и удобни боси обувки за всеки ден',
                'description': 'Тези класически мъжки боси обувки са изработени от висококачествена естествена кожа. Предлагат отличен комфорт и поддръжка на стъпалото, като същевременно запазват елегантния външен вид. Идеални за офиса или ежедневното носене.',
                'is_featured': True,
                'sizes': [40, 41, 42, 43, 44, 45]
            },
            {
                'name': 'Дамски боси обувки с връзки',
                'slug': 'damski-bosi-obuvki-s-vrazki',
                'category': women_category,
                'price': Decimal('79.99'),
                'stock': 30,
                'gender': 'women',
                'material': 'Еко кожа',
                'color': 'Черен',
                'short_description': 'Стилни дамски боси обувки с връзки',
                'description': 'Модерни дамски боси обувки с връзки, които комбинират стил и комфорт. Изработени от качествена еко кожа с гъвкава подметка, която позволява естествено движение на стъпалото.',
                'is_featured': True,
                'sizes': [35, 36, 37, 38, 39, 40, 41]
            },
            {
                'name': 'Детски боси обувки - сини',
                'slug': 'detski-bosi-obuvki-sini',
                'category': kids_category,
                'price': Decimal('49.99'),
                'stock': 20,
                'gender': 'kids',
                'material': 'Текстил',
                'color': 'Син',
                'short_description': 'Ярки и удобни боси обувки за деца',
                'description': 'Специално проектирани за растящите крачета на децата. Тези боси обувки осигуряват правилното развитие на стъпалото и предлагат максимален комфорт при игра и движение.',
                'is_featured': False,
                'sizes': [28, 29, 30, 31, 32, 33, 34]
            },
            {
                'name': 'Спортни боси маратонки',
                'slug': 'sportni-bosi-maratonki',
                'category': sport_category,
                'price': Decimal('119.99'),
                'old_price': Decimal('149.99'),
                'stock': 15,
                'gender': 'unisex',
                'material': 'Мрежест текстил',
                'color': 'Сив',
                'short_description': 'Професионални боси маратонки за спорт',
                'description': 'Високотехнологични боси маратонки, проектирани за сериозни спортисти. Осигуряват отлична вентилация, гъвкавост и сцепление при всякакви условия.',
                'is_featured': True,
                'sizes': [38, 39, 40, 41, 42, 43, 44]
            },
        ]
        
        for product_data in products_data:
            # Извади размерите от данните
            sizes = product_data.pop('sizes')
            
            product, created = Product.objects.get_or_create(
                slug=product_data['slug'],
                defaults=product_data
            )
            
            if created:
                # Добави размерите
                for size_eu in sizes:
                    try:
                        size_obj = ShoeSize.objects.get(eu_size=size_eu)
                        product.sizes.add(size_obj)
                    except ShoeSize.DoesNotExist:
                        pass
                
                self.stdout.write(f'Създаден продукт: {product.name}')
