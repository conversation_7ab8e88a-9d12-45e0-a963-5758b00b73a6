# Generated by Django 5.2 on 2025-06-22 07:25

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Име')),
                ('slug', models.SlugField(max_length=100, unique=True, verbose_name='Slug')),
                ('description', models.TextField(blank=True, verbose_name='Описание')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Създадена на')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Обновена на')),
            ],
            options={
                'verbose_name': 'Категория',
                'verbose_name_plural': 'Категории',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='ShoeSize',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('size', models.CharField(max_length=10, unique=True, verbose_name='Размер')),
                ('eu_size', models.IntegerField(help_text='Европейски размер', verbose_name='EU размер')),
                ('us_size', models.CharField(blank=True, max_length=10, verbose_name='US размер')),
                ('uk_size', models.CharField(blank=True, max_length=10, verbose_name='UK размер')),
            ],
            options={
                'verbose_name': 'Размер',
                'verbose_name_plural': 'Размери',
                'ordering': ['eu_size'],
            },
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='Име')),
                ('slug', models.SlugField(max_length=200, unique=True, verbose_name='Slug')),
                ('description', models.TextField(verbose_name='Описание')),
                ('short_description', models.CharField(blank=True, max_length=300, verbose_name='Кратко описание')),
                ('price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Цена')),
                ('old_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='Стара цена')),
                ('stock', models.PositiveIntegerField(default=0, verbose_name='Наличност')),
                ('is_active', models.BooleanField(default=True, verbose_name='Активен')),
                ('is_featured', models.BooleanField(default=False, verbose_name='Препоръчан')),
                ('material', models.CharField(blank=True, max_length=100, verbose_name='Материал')),
                ('color', models.CharField(blank=True, max_length=50, verbose_name='Цвят')),
                ('gender', models.CharField(choices=[('men', 'Мъжки'), ('women', 'Дамски'), ('unisex', 'Унисекс'), ('kids', 'Детски')], default='unisex', max_length=20, verbose_name='Пол')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Създаден на')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Обновен на')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.category', verbose_name='Категория')),
                ('sizes', models.ManyToManyField(to='core.shoesize', verbose_name='Размери')),
            ],
            options={
                'verbose_name': 'Продукт',
                'verbose_name_plural': 'Продукти',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ProductImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(upload_to='products/', verbose_name='Снимка')),
                ('alt_text', models.CharField(blank=True, max_length=200, verbose_name='Alt текст')),
                ('is_main', models.BooleanField(default=False, verbose_name='Главна снимка')),
                ('order', models.PositiveIntegerField(default=0, verbose_name='Ред')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Създадена на')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='images', to='core.product', verbose_name='Продукт')),
            ],
            options={
                'verbose_name': 'Снимка на продукт',
                'verbose_name_plural': 'Снимки на продукти',
                'ordering': ['order', 'created_at'],
            },
        ),
    ]
