# Generated by Django 5.2 on 2025-06-22 07:54

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Order',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order_number', models.CharField(max_length=20, unique=True, verbose_name='Номер на поръчка')),
                ('status', models.CharField(choices=[('pending', 'Чакаща'), ('confirmed', 'Потвърдена'), ('processing', 'Обработва се'), ('shipped', 'Изпратена'), ('delivered', 'Доставена'), ('cancelled', 'Отказана')], default='pending', max_length=20, verbose_name='Статус')),
                ('email', models.EmailField(max_length=254, verbose_name='Имейл')),
                ('first_name', models.CharField(max_length=50, verbose_name='Име')),
                ('last_name', models.CharField(max_length=50, verbose_name='Фамилия')),
                ('phone', models.CharField(max_length=20, verbose_name='Телефон')),
                ('address', models.CharField(max_length=200, verbose_name='Адрес')),
                ('city', models.CharField(max_length=100, verbose_name='Град')),
                ('postal_code', models.CharField(max_length=20, verbose_name='Пощенски код')),
                ('country', models.CharField(default='България', max_length=100, verbose_name='Държава')),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Обща сума')),
                ('shipping_cost', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Цена за доставка')),
                ('notes', models.TextField(blank=True, verbose_name='Бележки')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Създадена на')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Обновена на')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='Потребител')),
            ],
            options={
                'verbose_name': 'Поръчка',
                'verbose_name_plural': 'Поръчки',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='OrderItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.PositiveIntegerField(verbose_name='Количество')),
                ('price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Цена')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='core.order', verbose_name='Поръчка')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.product', verbose_name='Продукт')),
            ],
            options={
                'verbose_name': 'Артикул от поръчка',
                'verbose_name_plural': 'Артикули от поръчки',
            },
        ),
    ]
