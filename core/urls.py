from django.urls import path
from django.conf import settings
from django.conf.urls.static import static

from . import views

app_name = "core"

urlpatterns = [
    # Начална страница
    path("", views.index, name="index"),
    # Каталог с продукти
    path("products/", views.product_list, name="product_list"),
    path("products/<slug:slug>/", views.product_detail, name="product_detail"),
    # Категории
    path("category/<slug:slug>/", views.category_detail, name="category_detail"),
    # Пазарска количка
    path("cart/", views.cart_detail, name="cart_detail"),
    path("cart/add/<int:product_id>/", views.cart_add, name="cart_add"),
    path("cart/remove/<int:product_id>/", views.cart_remove, name="cart_remove"),
    path("cart/update/<int:product_id>/", views.cart_update, name="cart_update"),
    path("cart/clear/", views.cart_clear, name="cart_clear"),
    # Checkout и поръчки
    path("checkout/", views.checkout, name="checkout"),
    path(
        "order/success/<str:order_number>/", views.order_success, name="order_success"
    ),
    path("order/<str:order_number>/", views.order_detail, name="order_detail"),
]
