from django.contrib import admin
from django.utils.html import format_html
from .models import Category, ShoeSize, Product, ProductImage, Order, OrderItem


class ProductImageInline(admin.TabularInline):
    """Inline за снимки на продукти"""

    model = ProductImage
    extra = 1
    fields = ("image", "alt_text", "is_main", "order")
    readonly_fields = ("image_preview",)

    def image_preview(self, obj):
        if obj.image:
            return format_html(
                '<img src="{}" style="max-height: 100px; max-width: 100px;" />',
                obj.image.url,
            )
        return "Няма снимка"

    image_preview.short_description = "Преглед"


@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    """Админ за категории"""

    list_display = ("name", "slug", "created_at")
    list_filter = ("created_at",)
    search_fields = ("name", "description")
    prepopulated_fields = {"slug": ("name",)}
    readonly_fields = ("created_at", "updated_at")


@admin.register(ShoeSize)
class ShoeSizeAdmin(admin.ModelAdmin):
    """Админ за размери"""

    list_display = ("size", "eu_size", "us_size", "uk_size")
    list_filter = ("eu_size",)
    search_fields = ("size", "eu_size")
    ordering = ("eu_size",)


@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    """Админ за продукти"""

    list_display = (
        "name",
        "category",
        "price",
        "stock",
        "is_active",
        "is_featured",
        "created_at",
    )
    list_filter = ("category", "is_active", "is_featured", "gender", "created_at")
    search_fields = ("name", "description", "short_description")
    prepopulated_fields = {"slug": ("name",)}
    readonly_fields = ("created_at", "updated_at", "main_image_preview")
    filter_horizontal = ("sizes",)

    fieldsets = (
        (
            "Основна информация",
            {
                "fields": (
                    "name",
                    "slug",
                    "category",
                    "short_description",
                    "description",
                )
            },
        ),
        ("Цени и наличност", {"fields": ("price", "old_price", "stock")}),
        ("Характеристики", {"fields": ("sizes", "material", "color", "gender")}),
        ("Настройки", {"fields": ("is_active", "is_featured")}),
        (
            "Времеви полета",
            {"fields": ("created_at", "updated_at"), "classes": ("collapse",)},
        ),
        (
            "Главна снимка",
            {"fields": ("main_image_preview",), "classes": ("collapse",)},
        ),
    )

    inlines = [ProductImageInline]

    def main_image_preview(self, obj):
        """Показва преглед на главната снимка"""
        main_image = obj.main_image
        if main_image:
            return format_html(
                '<img src="{}" style="max-height: 200px; max-width: 200px;" />',
                main_image.image.url,
            )
        return "Няма главна снимка"

    main_image_preview.short_description = "Главна снимка"


@admin.register(ProductImage)
class ProductImageAdmin(admin.ModelAdmin):
    """Админ за снимки на продукти"""

    list_display = ("product", "alt_text", "is_main", "order", "image_preview")
    list_filter = ("is_main", "created_at")
    search_fields = ("product__name", "alt_text")
    list_editable = ("is_main", "order")

    def image_preview(self, obj):
        if obj.image:
            return format_html(
                '<img src="{}" style="max-height: 100px; max-width: 100px;" />',
                obj.image.url,
            )
        return "Няма снимка"

    image_preview.short_description = "Преглед"


class OrderItemInline(admin.TabularInline):
    """Inline за артикули в поръчка"""

    model = OrderItem
    extra = 0
    readonly_fields = ("total_price",)
    fields = ("product", "quantity", "price", "total_price")

    def total_price(self, obj):
        if obj.id:
            return f"{obj.total_price} лв."
        return "-"

    total_price.short_description = "Общо"


@admin.register(Order)
class OrderAdmin(admin.ModelAdmin):
    """Админ за поръчки"""

    list_display = (
        "order_number",
        "full_name",
        "email",
        "status",
        "total_with_shipping",
        "created_at",
    )
    list_filter = ("status", "created_at", "country", "city")
    search_fields = ("order_number", "email", "first_name", "last_name", "phone")
    readonly_fields = (
        "order_number",
        "created_at",
        "updated_at",
        "total_with_shipping",
    )

    fieldsets = (
        ("Основна информация", {"fields": ("order_number", "user", "status")}),
        (
            "Контактна информация",
            {"fields": ("first_name", "last_name", "email", "phone")},
        ),
        (
            "Адрес за доставка",
            {"fields": ("address", "city", "postal_code", "country")},
        ),
        (
            "Финансова информация",
            {"fields": ("total_amount", "shipping_cost", "total_with_shipping")},
        ),
        ("Допълнителна информация", {"fields": ("notes",)}),
        (
            "Времеви полета",
            {"fields": ("created_at", "updated_at"), "classes": ("collapse",)},
        ),
    )

    inlines = [OrderItemInline]

    def total_with_shipping(self, obj):
        return f"{obj.total_with_shipping} лв."

    total_with_shipping.short_description = "Общо с доставка"


@admin.register(OrderItem)
class OrderItemAdmin(admin.ModelAdmin):
    """Админ за артикули от поръчки"""

    list_display = ("order", "product", "quantity", "price", "total_price_display")
    list_filter = ("order__status", "order__created_at")
    search_fields = ("order__order_number", "product__name")
    readonly_fields = ("total_price_display",)

    def total_price_display(self, obj):
        return f"{obj.total_price} лв."

    total_price_display.short_description = "Общо"
