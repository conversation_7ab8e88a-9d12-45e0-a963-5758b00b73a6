#!/bin/bash

# Deployment script for Bos Svqt Django project
# Usage: ./deploy.sh

echo "🚀 Starting deployment process..."

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "❌ Virtual environment not found. Please create one first."
    exit 1
fi

# Activate virtual environment
echo "📦 Activating virtual environment..."
source venv/bin/activate

# Install/update dependencies
echo "📦 Installing dependencies..."
pip install -r requirements.txt

# Set production environment
export DJANGO_SETTINGS_MODULE=config.settings_production

# Run migrations
echo "🗃️  Running database migrations..."
python manage.py migrate

# Collect static files
echo "📁 Collecting static files..."
python manage.py collectstatic --noinput

# Create superuser if it doesn't exist
echo "👤 Checking for superuser..."
python manage.py shell -c "
from django.contrib.auth.models import User
if not User.objects.filter(is_superuser=True).exists():
    print('Creating superuser...')
    User.objects.create_superuser('admin', '<EMAIL>', 'admin123')
    print('Superuser created: admin/admin123')
else:
    print('Superuser already exists')
"

# Run tests
echo "🧪 Running tests..."
python manage.py test

if [ $? -eq 0 ]; then
    echo "✅ All tests passed!"
else
    echo "❌ Tests failed! Deployment aborted."
    exit 1
fi

# Check for any issues
echo "🔍 Running system checks..."
python manage.py check --deploy

echo "✅ Deployment completed successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Configure your web server (Nginx/Apache)"
echo "2. Set up Gunicorn or uWSGI"
echo "3. Configure SSL certificates"
echo "4. Set up monitoring and backups"
echo ""
echo "🌐 To start the development server:"
echo "python manage.py runserver"
echo ""
echo "🌐 To start with Gunicorn (production):"
echo "gunicorn config.wsgi:application --bind 0.0.0.0:8000"
